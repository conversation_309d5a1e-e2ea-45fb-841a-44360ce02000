---
import { t } from "i18next";
---

<section id="processo" class="process-section">
    <div class="container">
        <h2 class="section-title">{t("process.title")}</h2>
        <p class="section-subtitle">{t("process.subtitle")}</p>

        <div class="process-grid">
            <div class="process-step">
                <div class="number" data-number="1">1</div>
                <h3>{t("process.step1_title")}</h3>
                <p>{t("process.step1_desc")}</p>
            </div>
            <div class="process-step">
                <div class="number" data-number="2">02</div>
                <h3>{t("process.step2_title")}</h3>
                <p>{t("process.step2_desc")}</p>
            </div>
            <div class="process-step">
                <div class="number" data-number="3">03</div>
                <h3>{t("process.step3_title")}</h3>
                <p>{t("process.step3_desc")}</p>
            </div>
        </div>
    </div>
</section>


