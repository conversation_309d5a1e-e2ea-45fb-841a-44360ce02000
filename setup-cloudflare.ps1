# Setup Cloudflare per Agentik.ai
Write-Host "Setup Cloudflare per Agentik.ai" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

# Verifica che wrangler sia installato
try {
    wrangler --version | Out-Null
    Write-Host "Wrangler trovato" -ForegroundColor Green
} catch {
    Write-Host "Wrangler non trovato. Installazione..." -ForegroundColor Red
    npm install -g wrangler
}

# Verifica versione Node.js
$nodeVersion = (node -v).Substring(1).Split('.')[0]
if ([int]$nodeVersion -lt 20) {
    Write-Host "Node.js v20+ richiesto. Versione attuale: $(node -v)" -ForegroundColor Red
    Write-Host "Aggiorna Node.js e riprova." -ForegroundColor Red
    exit 1
}

Write-Host "Prerequisiti verificati" -ForegroundColor Green

# Login a Cloudflare
Write-Host "Login a Cloudflare..." -ForegroundColor Yellow
wrangler login

# Crea database D1
Write-Host "Creazione database D1..." -ForegroundColor Yellow
$dbOutput = wrangler d1 create agentik-contacts
Write-Host $dbOutput

# Estrai database ID (parsing semplificato)
$dbId = ($dbOutput | Select-String 'database_id = "([^"]*)"').Matches[0].Groups[1].Value
Write-Host "Database ID: $dbId" -ForegroundColor Cyan

# Crea KV namespace
Write-Host "Creazione KV namespace..." -ForegroundColor Yellow
$kvOutput = wrangler kv:namespace create "CACHE"
Write-Host $kvOutput

# Estrai KV ID
$kvId = ($kvOutput | Select-String 'id = "([^"]*)"').Matches[0].Groups[1].Value
Write-Host "KV ID: $kvId" -ForegroundColor Cyan

# Crea KV namespace per preview
Write-Host "Creazione KV namespace preview..." -ForegroundColor Yellow
$kvPreviewOutput = wrangler kv:namespace create "CACHE" --preview
$kvPreviewId = ($kvPreviewOutput | Select-String 'id = "([^"]*)"').Matches[0].Groups[1].Value
Write-Host "KV Preview ID: $kvPreviewId" -ForegroundColor Cyan

# Aggiorna wrangler.toml
Write-Host "Aggiornamento wrangler.toml..." -ForegroundColor Yellow
$content = Get-Content wrangler.toml -Raw
$content = $content -replace "INSERISCI_QUI_IL_DATABASE_ID", $dbId
$content = $content -replace "INSERISCI_QUI_IL_KV_ID", $kvId
$content = $content -replace "INSERISCI_QUI_IL_KV_PREVIEW_ID", $kvPreviewId
Set-Content wrangler.toml -Value $content

# Applica schema database
Write-Host "Applicazione schema database..." -ForegroundColor Yellow
wrangler d1 execute agentik-contacts --file=./schema.sql

# Verifica setup
Write-Host "Verifica setup..." -ForegroundColor Yellow
wrangler d1 execute agentik-contacts --command="SELECT name FROM sqlite_master WHERE type='table';"

Write-Host ""
Write-Host "Setup completato!" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host "Database ID: $dbId" -ForegroundColor Cyan
Write-Host "KV ID: $kvId" -ForegroundColor Cyan
Write-Host "KV Preview ID: $kvPreviewId" -ForegroundColor Cyan
Write-Host ""
Write-Host "Ora puoi fare il deploy con:" -ForegroundColor Yellow
Write-Host "npm run deploy" -ForegroundColor White
Write-Host ""
Write-Host "Dashboard disponibile su:" -ForegroundColor Yellow
Write-Host "https://tuo-sito.pages.dev/dashboard" -ForegroundColor White
