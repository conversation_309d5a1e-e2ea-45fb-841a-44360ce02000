<!DOCTYPE html>
<html lang="it">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contattaci | Aura Intelligence</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600;700;800&family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <style>
        /* Copied from index.html - ideally this would be in a shared CSS file */
        *,
        *::before,
        *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: '"Work Sans"', sans-serif;
            background-color: #0B0B0F;
            color: #E0E0E0;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            padding-top: 80px;
            /* Space for fixed header */
        }

        img,
        picture,
        video,
        canvas,
        svg {
            display: block;
            max-width: 100%;
        }

        h1,
        h2,
        h3 {
            font-family: 'Plus Jakarta Sans', 'Work Sans', sans-serif;
            line-height: 1.2;
            color: #FFFFFF;
        }

        h1 {
            font-weight: bold;
        }

        h2,
        h3 {
            font-weight: 600;
        }

        :root {
            --accent-color: #4A55FF;
            --accent-hover: #6A71FF;
            --bg-color: #0B0B0F;
            --surface-color-transparent: rgba(26, 26, 34, 0.5);
            --surface-color: rgb(26, 26, 34);
            --border-color: #2D2D3A;
            --text-primary: #FFFFFF;
            --text-secondary: #b3b5bd;
            --container-width: 1320px;
        }

        .container {
            width: 90%;
            max-width: var(--container-width);
            margin: 0 auto;
            padding: 120px 0;
        }

        .section-title {
            font-size: 48px;
            text-align: center;
            margin-bottom: 16px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }

        .section-subtitle {
            font-size: 18px;
            text-align: center;
            color: var(--text-secondary);
            max-width: 800px;
            margin: 0 auto 64px auto;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
            color: var(--text-primary);
            padding: 16px 32px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(74, 85, 255, 0.3);
            border: none;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(74, 85, 255, 0.4);
        }

        .secondary-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background-color: var(--surface-color-transparent);
            color: var(--text-primary);
            padding: 16px 32px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 500;
            font-size: 16px;
            border: 2px solid var(--accent-color);
            transition: all 0.3s ease;
            position: relative;
            opacity: 1;
        }

        .secondary-button:hover {
            background-color: var(--surface-color);
            opacity: 1;
            border-color: var(--accent-hover);
            transform: translateY(-2px);
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 5%;
            background-color: rgba(11, 11, 15, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid transparent;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .header.scrolled {
            background-color: rgba(11, 11, 15, 0.98);
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: var(--container-width);
            margin: 0 auto;
        }

        .logo {
            font-size: 22px;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .logo:hover {
            color: var(--accent-color);
        }

        .nav-desktop {
            display: flex;
            gap: 32px;
            align-items: center;
        }

        .nav-desktop a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .nav-desktop a:hover {
            color: var(--text-primary);
        }

        .nav-desktop a.active {
            color: var(--text-primary);
            font-weight: 600;
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 10px;
            z-index: 1001;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        .hamburger:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .hamburger span {
            width: 24px;
            height: 2px;
            background-color: var(--text-primary);
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }

        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        .nav-mobile {
            display: none;
            position: fixed;
            top: 0;
            right: -100%;
            width: 280px;
            height: 100vh;
            background-color: var(--surface-color);
            border-left: 1px solid var(--border-color);
            padding: 100px 32px 32px 32px;
            transition: right 0.3s ease;
            z-index: 999;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
        }

        .nav-mobile.active {
            right: 0;
        }

        .nav-mobile a {
            display: block;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            font-size: 18px;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color);
            transition: color 0.3s ease;
        }

        .nav-mobile a:hover {
            color: var(--accent-color);
        }

        .nav-mobile a.active {
            color: var(--accent-color);
            font-weight: 600;
        }

        .nav-mobile a:last-child {
            border-bottom: none;
        }

        .nav-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .nav-overlay.active {
            display: block;
        }

        .footer {
            border-top: 1px solid var(--border-color);
            padding: 40px 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .footer .container {
            padding: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .footer-text {
            flex: 1;
        }

        .footer-logo {
            flex-shrink: 0;
            margin-left: 20px;
        }

        .footer-logo a {
            display: inline-block;
            transition: transform 0.3s ease;
        }

        .footer-logo a:hover {
            transform: scale(1.05);
        }

        .footer-logo img {
            height: 40px;
            width: auto;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .footer-logo a:hover img {
            opacity: 1;
        }

        /* Contact Form Specific Styles */
        .contact-form-section {
            padding: 120px 0;
            min-height: calc(100vh - 160px);
            display: flex;
            align-items: center;
            position: relative;
            background:
                linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                radial-gradient(ellipse at 20% 80%, rgba(80, 26, 200, 0.15) 0%, transparent 60%),
                radial-gradient(ellipse at 80% 20%, rgba(179, 102, 207, 0.12) 0%, transparent 70%),
                radial-gradient(ellipse at 50% 50%, rgba(67, 83, 255, 0.10) 0%, transparent 80%);
            border-top: 1px solid var(--border-color);
            overflow: hidden;
            animation: subtleBackgroundShift 45s ease-in-out infinite;
        }

        .contact-form-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(45deg, transparent 30%, rgba(80, 26, 200, 0.08) 50%, transparent 70%),
                linear-gradient(-45deg, transparent 40%, rgba(179, 102, 207, 0.06) 60%, transparent 80%),
                linear-gradient(90deg, transparent 35%, rgba(67, 83, 255, 0.06) 55%, transparent 75%);
            z-index: 1;
            animation: subtleColorShift 35s ease-in-out infinite;
        }

        .contact-form-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 40%, rgba(80, 26, 200, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 70% 60%, rgba(179, 102, 207, 0.05) 0%, transparent 55%),
                radial-gradient(circle at 50% 80%, rgba(67, 83, 255, 0.04) 0%, transparent 60%);
            z-index: 2;
            animation: subtleParticleFloat 40s ease-in-out infinite;
        }

        .contact-form-container {
            max-width: 800px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 34, 0.8) 100%);
            padding: 60px;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 3;
            backdrop-filter: blur(10px);
        }

        .contact-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-color), var(--accent-hover));
            border-radius: 20px 20px 0 0;
        }

        .contact-form-container .section-title {
            font-size: 42px;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #FFFFFF 0%, #E0E0E0 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .contact-form-container .section-subtitle {
            font-size: 18px;
            margin-bottom: 40px;
            color: var(--text-secondary);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            color: var(--text-primary);
            margin-bottom: 10px;
            font-weight: 600;
            font-family: 'Plus Jakarta Sans', 'Work Sans', sans-serif;
            letter-spacing: 0.02em;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 16px 20px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            background: linear-gradient(135deg, var(--bg-color) 0%, rgba(11, 11, 15, 0.8) 100%);
            color: var(--text-primary);
            font-size: 16px;
            font-family: '"Work Sans"', sans-serif;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(74, 85, 255, 0.2), 0 4px 20px rgba(74, 85, 255, 0.1);
            transform: translateY(-2px);
        }

        .form-group input:hover,
        .form-group textarea:hover {
            border-color: rgba(74, 85, 255, 0.5);
            transform: translateY(-1px);
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 24px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .form-group select {
            width: 100%;
            padding: 16px 20px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            background: linear-gradient(135deg, var(--bg-color) 0%, rgba(11, 11, 15, 0.8) 100%);
            color: var(--text-primary);
            font-size: 16px;
            font-family: '"Work Sans"', sans-serif;
            transition: all 0.3s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23b3b5bd'%3E%3Cpath d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 20px center;
            background-size: 1em;
            backdrop-filter: blur(5px);
        }

        .form-group select:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(74, 85, 255, 0.2), 0 4px 20px rgba(74, 85, 255, 0.1);
            transform: translateY(-2px);
        }

        .form-group select:hover {
            border-color: rgba(74, 85, 255, 0.5);
            transform: translateY(-1px);
        }

        .form-group select option {
            background-color: #1a1a1a;
            color: #ffffff;
            padding: 12px;
            border: none;
        }

        .form-group select option:hover {
            background-color: var(--accent-color);
            color: #ffffff;
        }

        .form-group select option:checked {
            background-color: var(--accent-color);
            color: #ffffff;
        }

        /* ------------------- */
        /* --- Language Selector --- */
        /* ------------------- */
        .language-selector {
            position: relative;
            margin-left: 20px;
        }

        .language-current {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .language-current:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .language-current .flag {
            font-size: 16px;
        }

        .language-current .lang-code {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .language-current .dropdown-arrow {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            transition: transform 0.3s ease;
        }

        .language-selector.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .language-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 8px;
            background: rgba(20, 20, 25, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 140px;
        }

        .language-selector.active .language-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .language-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: #ffffff;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 4px;
        }

        .language-option:hover {
            background: rgba(139, 92, 246, 0.2);
            color: #ffffff;
            transform: translateX(4px);
        }

        .language-option.active {
            background: rgba(139, 92, 246, 0.3);
            color: #ffffff;
        }

        .language-option .flag {
            font-size: 16px;
        }

        .language-option .lang-name {
            font-size: 14px;
            font-weight: 500;
        }

        /* Mobile Language Selector */
        .mobile-language-selector {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .mobile-language-title {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .mobile-language-options {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .mobile-language-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            color: #ffffff;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        .mobile-language-option:hover {
            color: var(--accent-color);
            transform: translateX(8px);
        }

        .mobile-language-option.active {
            color: var(--accent-color);
        }

        .mobile-language-option .flag {
            font-size: 18px;
        }

        .mobile-language-option .lang-name {
            font-size: 16px;
            font-weight: 500;
        }

        .form-group.required label::after {
            content: ' *';
            color: var(--accent-color);
        }

        .form-intro-text {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 32px;
            text-align: left;
            padding: 24px;
            background: rgba(74, 85, 255, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--accent-color);
        }

        /* Animation Keyframes */
        @keyframes subtleBackgroundShift {

            0%,
            100% {
                background:
                    linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                    radial-gradient(ellipse at 20% 80%, rgba(80, 26, 200, 0.15) 0%, transparent 60%),
                    radial-gradient(ellipse at 80% 20%, rgba(179, 102, 207, 0.12) 0%, transparent 70%),
                    radial-gradient(ellipse at 50% 50%, rgba(67, 83, 255, 0.10) 0%, transparent 80%);
            }

            20% {
                background:
                    linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                    radial-gradient(ellipse at 60% 70%, rgba(179, 102, 207, 0.16) 0%, transparent 62%),
                    radial-gradient(ellipse at 40% 30%, rgba(67, 83, 255, 0.13) 0%, transparent 72%),
                    radial-gradient(ellipse at 80% 60%, rgba(80, 26, 200, 0.11) 0%, transparent 78%);
            }

            40% {
                background:
                    linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                    radial-gradient(ellipse at 70% 30%, rgba(179, 102, 207, 0.18) 0%, transparent 65%),
                    radial-gradient(ellipse at 30% 70%, rgba(67, 83, 255, 0.14) 0%, transparent 75%),
                    radial-gradient(ellipse at 90% 90%, rgba(80, 26, 200, 0.12) 0%, transparent 70%);
            }

            60% {
                background:
                    linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                    radial-gradient(ellipse at 40% 20%, rgba(67, 83, 255, 0.17) 0%, transparent 63%),
                    radial-gradient(ellipse at 80% 80%, rgba(80, 26, 200, 0.13) 0%, transparent 73%),
                    radial-gradient(ellipse at 20% 40%, rgba(179, 102, 207, 0.11) 0%, transparent 82%);
            }

            80% {
                background:
                    linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                    radial-gradient(ellipse at 60% 40%, rgba(67, 83, 255, 0.16) 0%, transparent 62%),
                    radial-gradient(ellipse at 40% 60%, rgba(80, 26, 200, 0.14) 0%, transparent 68%),
                    radial-gradient(ellipse at 10% 10%, rgba(179, 102, 207, 0.10) 0%, transparent 85%);
            }
        }

        @keyframes subtleColorShift {

            0%,
            100% {
                background:
                    linear-gradient(45deg, transparent 30%, rgba(80, 26, 200, 0.08) 50%, transparent 70%),
                    linear-gradient(-45deg, transparent 40%, rgba(179, 102, 207, 0.06) 60%, transparent 80%),
                    linear-gradient(90deg, transparent 35%, rgba(67, 83, 255, 0.06) 55%, transparent 75%);
            }

            50% {
                background:
                    linear-gradient(45deg, transparent 25%, rgba(179, 102, 207, 0.10) 55%, transparent 75%),
                    linear-gradient(-45deg, transparent 35%, rgba(67, 83, 255, 0.08) 65%, transparent 85%),
                    linear-gradient(90deg, transparent 30%, rgba(80, 26, 200, 0.08) 60%, transparent 80%);
            }
        }

        @keyframes subtleParticleFloat {

            0%,
            100% {
                background:
                    radial-gradient(circle at 30% 40%, rgba(80, 26, 200, 0.06) 0%, transparent 50%),
                    radial-gradient(circle at 70% 60%, rgba(179, 102, 207, 0.05) 0%, transparent 55%),
                    radial-gradient(circle at 50% 80%, rgba(67, 83, 255, 0.04) 0%, transparent 60%);
            }

            33% {
                background:
                    radial-gradient(circle at 60% 20%, rgba(179, 102, 207, 0.07) 0%, transparent 45%),
                    radial-gradient(circle at 20% 70%, rgba(67, 83, 255, 0.06) 0%, transparent 50%),
                    radial-gradient(circle at 80% 50%, rgba(80, 26, 200, 0.05) 0%, transparent 65%);
            }

            66% {
                background:
                    radial-gradient(circle at 40% 80%, rgba(67, 83, 255, 0.06) 0%, transparent 48%),
                    radial-gradient(circle at 80% 30%, rgba(80, 26, 200, 0.056) 0%, transparent 52%),
                    radial-gradient(circle at 10% 60%, rgba(179, 102, 207, 0.044) 0%, transparent 58%);
            }
        }

        /* Floating Particles */
        .contact-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(80, 26, 200, 0.6);
            border-radius: 50%;
            animation: floatUp 20s linear infinite;
        }

        .particle:nth-child(2n) {
            background: rgba(179, 102, 207, 0.4);
            animation-duration: 40s;
            animation-delay: -5s;
        }

        .particle:nth-child(3n) {
            background: rgba(67, 83, 255, 0.5);
            animation-duration: 18s;
            animation-delay: -10s;
        }

        /* Data Stream Effect */
        .contact-datastream {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .data-line {
            position: absolute;
            width: 1px;
            height: 100px;
            background: linear-gradient(to bottom, transparent, rgba(80, 26, 200, 0.8), transparent);
            animation: dataFlow 10s linear infinite;
        }

        .data-line:nth-child(2n) {
            background: linear-gradient(to bottom, transparent, rgba(179, 102, 207, 0.6), transparent);
            animation-duration: 12s;
            animation-delay: -3s;
        }

        .data-line:nth-child(3n) {
            background: linear-gradient(to bottom, transparent, rgba(67, 83, 255, 0.7), transparent);
            animation-duration: 10s;
            animation-delay: -6s;
        }

        @keyframes floatUp {
            0% {
                transform: translateY(100vh) translateX(0px) rotate(0deg);
                opacity: 0;
            }

            10% {
                opacity: 1;
            }

            90% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) translateX(100px) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes dataFlow {
            0% {
                transform: translateY(-100px);
                opacity: 0;
            }

            10%,
            90% {
                opacity: 1;
            }

            100% {
                transform: translateY(calc(100vh + 100px));
                opacity: 0;
            }
        }



        /* Responsive adjustments */
        @media (max-width: 900px) {
            .nav-desktop {
                display: none;
            }

            .hamburger {
                display: flex;
            }

            .nav-mobile {
                display: block;
            }
        }

        @media (max-width: 600px) {
            body {
                padding-top: 70px;
            }

            .header {
                padding: 12px 5%;
            }

            .logo {
                font-size: 20px;
            }

            .hamburger span {
                width: 22px;
            }

            .nav-mobile {
                width: 100%;
                padding: 90px 24px 24px 24px;
            }

            .container {
                padding: 80px 0;
            }

            .contact-form-section {
                padding: 60px 0;
                background-attachment: scroll;
            }

            .contact-form-container {
                padding: 40px;
                margin: 0 5%;
                width: 90%;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .section-title {
                font-size: 32px;
            }

            .contact-form-container .section-title {
                font-size: 28px;
            }

            .contact-form-container .section-subtitle {
                font-size: 15px;
            }

            .form-intro-text {
                font-size: 15px;
                padding: 20px;
            }

            /* Reduce animations on mobile for performance */
            .particle {
                display: none;
            }

            .data-line {
                animation-duration: 20s;
            }

            /* Footer responsive */
            .footer .container {
                flex-direction: column;
                text-align: center;
            }

            .footer-content {
                flex-direction: column;
                gap: 20px;
            }

            .footer-logo {
                margin-left: 0;
            }

            .footer-logo img {
                height: 35px;
            }
        }
    </style>
</head>

<body>

    <header class="header">
        <div class="header-container">
            <a href="index.html#hero" class="logo">Agentik.ai</a>

            <nav class="nav-desktop">
                <a href="index.html#hero">Home</a>
                <a href="index.html#problemi">Problemi</a>
                <a href="index.html#processo">Processo</a>
                <a href="index.html#soluzioni">Soluzioni</a>
                <a href="index.html#case-studies">Risultati</a>
                <a href="contact.html" class="secondary-button active">Contatti</a>

                <!-- Language Selector -->
                <div class="language-selector">
                    <div class="language-current" onclick="toggleLanguageMenu()">
                        <span class="flag">🇮🇹</span>
                        <span class="lang-code">IT</span>
                        <span class="dropdown-arrow">▼</span>
                    </div>
                    <div class="language-dropdown">
                        <a href="#" class="language-option active" data-lang="it">
                            <span class="flag">🇮🇹</span>
                            <span class="lang-name">Italiano</span>
                        </a>
                        <a href="#" class="language-option" data-lang="en">
                            <span class="flag">🇺🇸</span>
                            <span class="lang-name">English</span>
                        </a>
                        <a href="#" class="language-option" data-lang="es">
                            <span class="flag">🇪🇸</span>
                            <span class="lang-name">Español</span>
                        </a>
                    </div>
                </div>
            </nav>

            <div class="hamburger" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <div class="nav-overlay" onclick="closeMobileMenu()"></div>

    <nav class="nav-mobile">
        <a href="index.html#hero" onclick="closeMobileMenu()">Home</a>
        <a href="index.html#problemi" onclick="closeMobileMenu()">Problemi</a>
        <a href="index.html#processo" onclick="closeMobileMenu()">Processo</a>
        <a href="index.html#soluzioni" onclick="closeMobileMenu()">Soluzioni</a>
        <a href="index.html#case-studies" onclick="closeMobileMenu()">Risultati</a>
        <a href="contact.html" class="active" onclick="closeMobileMenu()">Contatti</a>

        <!-- Mobile Language Selector -->
        <div class="mobile-language-selector">
            <div class="mobile-language-title">Lingua / Language / Idioma</div>
            <div class="mobile-language-options">
                <a href="#" class="mobile-language-option active" data-lang="it">
                    <span class="flag">🇮🇹</span>
                    <span class="lang-name">Italiano</span>
                </a>
                <a href="#" class="mobile-language-option" data-lang="en">
                    <span class="flag">🇺🇸</span>
                    <span class="lang-name">English</span>
                </a>
                <a href="#" class="mobile-language-option" data-lang="es">
                    <span class="flag">🇪🇸</span>
                    <span class="lang-name">Español</span>
                </a>
            </div>
        </div>
    </nav>

    <main>
        <section class="contact-form-section">
            <!-- Floating Particles -->
            <div class="contact-particles">
                <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
                <div class="particle" style="left: 20%; animation-delay: -2s;"></div>
                <div class="particle" style="left: 30%; animation-delay: -4s;"></div>
                <div class="particle" style="left: 40%; animation-delay: -6s;"></div>
                <div class="particle" style="left: 50%; animation-delay: -8s;"></div>
                <div class="particle" style="left: 60%; animation-delay: -10s;"></div>
                <div class="particle" style="left: 70%; animation-delay: -12s;"></div>
                <div class="particle" style="left: 80%; animation-delay: -14s;"></div>
                <div class="particle" style="left: 90%; animation-delay: -16s;"></div>
            </div>

            <!-- Data Stream Effect -->
            <div class="contact-datastream">
                <div class="data-line" style="left: 15%; animation-delay: 0s;"></div>
                <div class="data-line" style="left: 35%; animation-delay: -2s;"></div>
                <div class="data-line" style="left: 55%; animation-delay: -4s;"></div>
                <div class="data-line" style="left: 75%; animation-delay: -6s;"></div>
                <div class="data-line" style="left: 85%; animation-delay: -8s;"></div>
            </div>

            <div class="contact-form-container">
                <h1 class="section-title">Conosciamoci Meglio</h1>
                <p class="section-subtitle">Parla con un esperto e inizia a pianificare il tuo progetto.</p>

                <div class="form-intro-text">
                    <p>In Agentik.ai, crediamo che la tecnologia debba potenziare le persone. Le nostre soluzioni di IA
                        sono progettate per liberarti dai compiti ripetitivi, permettendoti di concentrarti sulla
                        creatività e strategia che solo l'ingegno umano può offrire.</p>
                </div>

                <form action="#" method="POST">
                    <div class="form-row">
                        <div class="form-group required">
                            <label for="nome_completo">Nome Completo</label>
                            <input type="text" id="nome_completo" name="nome_completo" placeholder="Mario Rossi"
                                required>
                        </div>
                        <div class="form-group required">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group required">
                            <label for="ruolo">Il Tuo Ruolo in Azienda</label>
                            <input type="text" id="ruolo" name="ruolo" placeholder="es. CTO, Project Manager, ecc."
                                required>
                        </div>
                        <div class="form-group required">
                            <label for="nome_azienda">Nome Azienda</label>
                            <input type="text" id="nome_azienda" name="nome_azienda"
                                placeholder="Nome della tua azienda" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sito_web">Sito Web (Opzionale)</label>
                            <input type="url" id="sito_web" name="sito_web" placeholder="https://latuaazienda.com">
                        </div>
                        <div class="form-group">
                            <label for="telefono">Numero di Telefono (Opzionale)</label>
                            <input type="tel" id="telefono" name="telefono" placeholder="+39 333 1234567">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group required">
                            <label for="dimensione_azienda">Dimensione Azienda</label>
                            <select id="dimensione_azienda" name="dimensione_azienda" required>
                                <option value="" disabled selected>Seleziona dimensione azienda</option>
                                <option value="1-10">1-10 dipendenti</option>
                                <option value="11-50">11-50 dipendenti</option>
                                <option value="51-200">51-200 dipendenti</option>
                                <option value="201-1000">201-1000 dipendenti</option>
                                <option value="1000+">Oltre 1000 dipendenti</option>
                            </select>
                        </div>
                        <div class="form-group required">
                            <label for="fatturato_annuo">Fatturato Annuo</label>
                            <select id="fatturato_annuo" name="fatturato_annuo" required>
                                <option value="" disabled selected>Seleziona fatturato annuo</option>
                                <option value="<1M">Meno di 1 milione €</option>
                                <option value="1M-5M">1-5 milioni €</option>
                                <option value="5M-20M">5-20 milioni €</option>
                                <option value="20M-100M">20-100 milioni €</option>
                                <option value="100M+">Oltre 100 milioni €</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group required">
                        <label for="budget_progetto">Budget di Progetto</label>
                        <select id="budget_progetto" name="budget_progetto" required>
                            <option value="" disabled selected>Seleziona budget di progetto</option>
                            <option value="<5k">Meno di 5.000 €</option>
                            <option value="5k-15k">5.000 € - 15.000 €</option>
                            <option value="15k-30k">15.000 € - 30.000 €</option>
                            <option value="30k-50k">30.000 € - 50.000 €</option>
                            <option value="50k+">Oltre 50.000 €</option>
                            <option value="nda">Da definire</option>
                        </select>
                    </div>
                    <div class="form-group required">
                        <label for="come_possiamo_aiutare">Come Possiamo Aiutarti?</label>
                        <textarea id="come_possiamo_aiutare" name="come_possiamo_aiutare" rows="5"
                            placeholder="Descrivi le esigenze del tuo progetto e come possiamo assisterti."
                            required></textarea>
                    </div>
                    <button type="submit" class="cta-button" style="width: 100%;">Invia Richiesta</button>
                </form>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2025 Agentik.ai. Tutti i diritti riservati.</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const hamburger = document.querySelector('.hamburger');
            const mobileNav = document.querySelector('.nav-mobile');
            const overlay = document.querySelector('.nav-overlay');

            hamburger.classList.toggle('active');
            mobileNav.classList.toggle('active');
            overlay.classList.toggle('active');

            if (mobileNav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = 'auto';
            }
        }

        function closeMobileMenu() {
            const hamburger = document.querySelector('.hamburger');
            const mobileNav = document.querySelector('.nav-mobile');
            const overlay = document.querySelector('.nav-overlay');

            hamburger.classList.remove('active');
            mobileNav.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Language Selector Functions
        function toggleLanguageMenu() {
            const languageSelector = document.querySelector('.language-selector');
            languageSelector.classList.toggle('active');
        }

        // Handle language selection
        document.addEventListener('DOMContentLoaded', function() {
            const languageOptions = document.querySelectorAll('.language-option, .mobile-language-option');

            languageOptions.forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();

                    const selectedLang = this.getAttribute('data-lang');
                    const flag = this.querySelector('.flag').textContent;
                    const langCode = selectedLang.toUpperCase();

                    // Update desktop selector
                    const currentSelector = document.querySelector('.language-current');
                    if (currentSelector) {
                        currentSelector.querySelector('.flag').textContent = flag;
                        currentSelector.querySelector('.lang-code').textContent = langCode;
                    }

                    // Update active states
                    document.querySelectorAll('.language-option, .mobile-language-option').forEach(opt => {
                        opt.classList.remove('active');
                    });

                    document.querySelectorAll(`[data-lang="${selectedLang}"]`).forEach(opt => {
                        opt.classList.add('active');
                    });

                    // Close dropdown
                    const languageSelector = document.querySelector('.language-selector');
                    if (languageSelector) {
                        languageSelector.classList.remove('active');
                    }

                    // Close mobile menu if open
                    closeMobileMenu();

                    // Here you would typically handle the actual language change
                    console.log('Language changed to:', selectedLang);
                });
            });
        });

        document.querySelectorAll('a[href^="index.html#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                // If on contact.html and clicking a link to index.html section, just navigate
                if (window.location.pathname.includes('contact.html')) {
                    return;
                }
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(this.getAttribute('href').indexOf('#'));
                const target = document.querySelector(targetId);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        document.addEventListener('click', function (e) {
            const hamburger = document.querySelector('.hamburger');
            const mobileNav = document.querySelector('.nav-mobile');
            const languageSelector = document.querySelector('.language-selector');

            // Check if mobileNav exists before trying to check if it contains e.target
            if (mobileNav && !hamburger.contains(e.target) && !mobileNav.contains(e.target)) {
                closeMobileMenu();
            } else if (!mobileNav && !hamburger.contains(e.target)) { // If mobileNav is not on the page (e.g. desktop view)
                closeMobileMenu(); // This will do nothing if menu isn't active, but good practice
            }

            // Close language menu when clicking outside
            if (languageSelector && !languageSelector.contains(e.target)) {
                languageSelector.classList.remove('active');
            }
        });

        window.addEventListener('resize', function () {
            if (window.innerWidth > 900) {
                closeMobileMenu();
            }
        });

        let lastScrollTop = 0;
        const header = document.querySelector('.header');
        window.addEventListener('scroll', function () {
            let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            if (scrollTop > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            lastScrollTop = scrollTop;
        });
    </script>

</body>

</html>