---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="Contattaci | Aura Intelligence" description="Contatta Agentik.ai per trasformare la tua azienda con agenti IA personalizzati">
    <Header currentPage="contact" />
    <main>
        <section class="contact-form-section">
            <!-- Floating Particles -->
            <div class="contact-particles">
                <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
                <div class="particle" style="left: 20%; animation-delay: -2s;"></div>
                <div class="particle" style="left: 30%; animation-delay: -4s;"></div>
                <div class="particle" style="left: 40%; animation-delay: -6s;"></div>
                <div class="particle" style="left: 50%; animation-delay: -8s;"></div>
                <div class="particle" style="left: 60%; animation-delay: -10s;"></div>
                <div class="particle" style="left: 70%; animation-delay: -12s;"></div>
                <div class="particle" style="left: 80%; animation-delay: -14s;"></div>
                <div class="particle" style="left: 90%; animation-delay: -16s;"></div>
            </div>

            <!-- Data Stream Effect -->
            <div class="contact-datastream">
                <div class="data-line" style="left: 15%; animation-delay: 0s;"></div>
                <div class="data-line" style="left: 35%; animation-delay: -2s;"></div>
                <div class="data-line" style="left: 55%; animation-delay: -4s;"></div>
                <div class="data-line" style="left: 75%; animation-delay: -6s;"></div>
                <div class="data-line" style="left: 85%; animation-delay: -8s;"></div>
            </div>

            <div class="contact-form-container">
                <h1 class="section-title">Conosciamoci Meglio</h1>
                <p class="section-subtitle">Parla con un esperto e inizia a pianificare il tuo progetto.</p>

                <div class="form-intro-text">
                    <p>In Agentik.ai, crediamo che la tecnologia debba potenziare le persone. Le nostre soluzioni di IA sono progettate per liberarti dai compiti ripetitivi, permettendoti di concentrarti sulla creatività e strategia che solo l'ingegno umano può offrire.</p>
                </div>

                <form id="contact-form" action="/api/contact" method="POST">
                    <div class="form-row">
                        <div class="form-group required">
                            <label for="nome_completo">Nome Completo</label>
                            <input type="text" id="nome_completo" name="nome_completo" placeholder="Mario Rossi" required>
                        </div>
                        <div class="form-group required">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group required">
                            <label for="ruolo">Il Tuo Ruolo in Azienda</label>
                            <input type="text" id="ruolo" name="ruolo" placeholder="es. CTO, Project Manager, ecc." required>
                        </div>
                        <div class="form-group required">
                            <label for="nome_azienda">Nome Azienda</label>
                            <input type="text" id="nome_azienda" name="nome_azienda" placeholder="Nome della tua azienda" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sito_web">Sito Web (Opzionale)</label>
                            <input type="url" id="sito_web" name="sito_web" placeholder="https://latuaazienda.com">
                        </div>
                        <div class="form-group">
                            <label for="telefono">Numero di Telefono (Opzionale)</label>
                            <input type="tel" id="telefono" name="telefono" placeholder="+39 333 1234567">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group required">
                            <label for="dimensione_azienda">Dimensione Azienda</label>
                            <select id="dimensione_azienda" name="dimensione_azienda" required>
                                <option value="" disabled selected>Seleziona dimensione azienda</option>
                                <option value="1-10">1-10 dipendenti</option>
                                <option value="11-50">11-50 dipendenti</option>
                                <option value="51-200">51-200 dipendenti</option>
                                <option value="201-1000">201-1000 dipendenti</option>
                                <option value="1000+">Oltre 1000 dipendenti</option>
                            </select>
                        </div>
                        <div class="form-group required">
                            <label for="fatturato_annuo">Fatturato Annuo</label>
                            <select id="fatturato_annuo" name="fatturato_annuo" required>
                                <option value="" disabled selected>Seleziona fatturato annuo</option>
                                <option value="<1M">Meno di 1 milione €</option>
                                <option value="1M-5M">1-5 milioni €</option>
                                <option value="5M-20M">5-20 milioni €</option>
                                <option value="20M-100M">20-100 milioni €</option>
                                <option value="100M+">Oltre 100 milioni €</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group required">
                        <label for="budget_progetto">Budget di Progetto</label>
                        <select id="budget_progetto" name="budget_progetto" required>
                            <option value="" disabled selected>Seleziona budget di progetto</option>
                            <option value="<5k">Meno di 5.000 €</option>
                            <option value="5k-15k">5.000 € - 15.000 €</option>
                            <option value="15k-30k">15.000 € - 30.000 €</option>
                            <option value="30k-50k">30.000 € - 50.000 €</option>
                            <option value="50k+">Oltre 50.000 €</option>
                            <option value="nda">Da definire</option>
                        </select>
                    </div>
                    <div class="form-group required">
                        <label for="come_possiamo_aiutare">Come Possiamo Aiutarti?</label>
                        <textarea id="come_possiamo_aiutare" name="come_possiamo_aiutare" rows="5" placeholder="Descrivi le esigenze del tuo progetto e come possiamo assisterti." required></textarea>
                    </div>
                    <button type="submit" class="cta-button" id="submit-btn" style="width: 100%;">
                        <span id="btn-text">Invia Richiesta</span>
                        <span id="btn-arrow">→</span>
                    </button>
                </form>

                <!-- Messaggio di feedback -->
                <div id="form-message" style="display: none; margin-top: 20px; padding: 16px; border-radius: 8px; text-align: center;"></div>
            </div>
        </section>
    </main>
    <Footer />

    <script>
        document.getElementById('contact-form')?.addEventListener('submit', async function(e) {
            e.preventDefault();

            const form = e.target as HTMLFormElement;
            const submitBtn = document.getElementById('submit-btn') as HTMLButtonElement;
            const btnText = document.getElementById('btn-text') as HTMLSpanElement;
            const btnArrow = document.getElementById('btn-arrow') as HTMLSpanElement;
            const messageDiv = document.getElementById('form-message') as HTMLDivElement;

            // Mostra loading
            submitBtn.disabled = true;
            btnText.textContent = 'Invio in corso...';
            btnArrow.textContent = '⏳';
            messageDiv.style.display = 'none';

            try {
                const formData = new FormData(form);
                const response = await fetch('/api/contact', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    messageDiv.style.backgroundColor = 'rgba(34, 197, 94, 0.1)';
                    messageDiv.style.borderLeft = '3px solid #22c55e';
                    messageDiv.style.color = '#22c55e';
                    messageDiv.textContent = result.message;
                    form.reset();
                } else {
                    messageDiv.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
                    messageDiv.style.borderLeft = '3px solid #ef4444';
                    messageDiv.style.color = '#ef4444';
                    messageDiv.textContent = result.message;
                }

                messageDiv.style.display = 'block';

            } catch (error) {
                messageDiv.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
                messageDiv.style.borderLeft = '3px solid #ef4444';
                messageDiv.style.color = '#ef4444';
                messageDiv.textContent = 'Errore di connessione. Riprova più tardi.';
                messageDiv.style.display = 'block';
            } finally {
                // Ripristina il pulsante
                submitBtn.disabled = false;
                btnText.textContent = 'Invia Richiesta';
                btnArrow.textContent = '→';
            }
        });
    </script>
</Layout>


