---
import i18next, { t } from "i18next";
import { localizePath } from "astro-i18next";

const { currentPage = 'home' } = Astro.props;
---

<header class="header">
    <div class="header-container">
        <a href={localizePath("/")} class="logo">Agentik.ai</a>

        <!-- Desktop Navigation -->
        <nav class="nav-desktop">
            <a href="#hero">{t("nav.home")}</a>
            <a href="#problemi">{t("nav.problems")}</a>
            <a href="#processo">{t("nav.process")}</a>
            <a href="#soluzioni">{t("nav.solutions")}</a>
            <a href="#case-studies">{t("nav.results")}</a>

            <!-- Language Selector -->
            <select class="language-select" onchange="changeLanguage(this.value)">
                <option value="it">Italiano</option>
                <option value="en">English</option>
                <option value="es">Español</option>
            </select>

            <a href={localizePath("/contact")} class={`secondary-button ${currentPage === 'contact' ? 'active' : ''}`}>{t("nav.contact")}</a>
        </nav>

        <!-- Mobile Hamburger -->
        <div class="hamburger" onclick="toggleMobileMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>
</header>

<!-- Mobile Navigation Overlay -->
<div class="nav-overlay" onclick="closeMobileMenu()"></div>

<!-- Mobile Navigation Menu -->
<nav class="nav-mobile">
    <a href="#hero" onclick="closeMobileMenu()">{t("nav.home")}</a>
    <a href="#problemi" onclick="closeMobileMenu()">{t("nav.problems")}</a>
    <a href="#processo" onclick="closeMobileMenu()">{t("nav.process")}</a>
    <a href="#soluzioni" onclick="closeMobileMenu()">{t("nav.solutions")}</a>
    <a href="#case-studies" onclick="closeMobileMenu()">{t("nav.results")}</a>

    <!-- Mobile Language Selector -->
    <div class="mobile-language-selector">
        <div class="mobile-language-title">Lingua</div>
        <select class="mobile-language-select" onchange="changeLanguage(this.value)">
            <option value="it">Italiano</option>
            <option value="en">English</option>
            <option value="es">Español</option>
        </select>
    </div>

    <a href={localizePath("/contact")} class={currentPage === 'contact' ? 'active' : ''} onclick="closeMobileMenu()">{t("nav.contact")}</a>
</nav>


