---
const { currentPage = 'home' } = Astro.props;
---

<header class="header">
    <div class="header-container">
        <a href="#hero" class="logo">Agentik.ai</a>

        <!-- Desktop Navigation -->
        <nav class="nav-desktop">
            <a href="#hero">Home</a>
            <a href="#problemi">Problemi</a>
            <a href="#processo">Processo</a>
            <a href="#soluzioni">Soluzioni</a>
            <a href="#case-studies">Risultati</a>

            <!-- Language Selector -->
            <select class="language-select" onchange="changeLanguage(this.value)">
                <option value="it">Italiano</option>
                <option value="en">English</option>
                <option value="es">Español</option>
            </select>

            <a href="/contact" class={`secondary-button ${currentPage === 'contact' ? 'active' : ''}`}>Contatti</a>
        </nav>

        <!-- <PERSON> Hamburger -->
        <div class="hamburger" onclick="toggleMobileMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>
</header>

<!-- Mobile Navigation Overlay -->
<div class="nav-overlay" onclick="closeMobileMenu()"></div>

<!-- Mobile Navigation Menu -->
<nav class="nav-mobile">
    <a href="#hero" onclick="closeMobileMenu()">Home</a>
    <a href="#problemi" onclick="closeMobileMenu()">Problemi</a>
    <a href="#processo" onclick="closeMobileMenu()">Processo</a>
    <a href="#soluzioni" onclick="closeMobileMenu()">Soluzioni</a>
    <a href="#case-studies" onclick="closeMobileMenu()">Risultati</a>

    <!-- Mobile Language Selector -->
    <div class="mobile-language-selector">
        <div class="mobile-language-title">Lingua</div>
        <select class="mobile-language-select" onchange="changeLanguage(this.value)">
            <option value="it">Italiano</option>
            <option value="en">English</option>
            <option value="es">Español</option>
        </select>
    </div>

    <a href="/contact" class={currentPage === 'contact' ? 'active' : ''} onclick="closeMobileMenu()">Contatti</a>
</nav>


