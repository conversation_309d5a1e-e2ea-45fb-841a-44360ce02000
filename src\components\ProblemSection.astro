---
import i18next, { t } from "i18next";
// Problem Section component
---

<section id="problemi" class="problem-section">
    <div class="container" style="position: relative; z-index: 2;">
        <h2 class="section-title" set:html={t("problem.title")}></h2>
        <p class="section-subtitle">{t("problem.subtitle")}</p>
        <div class="problem-grid">
            <div class="problem-card">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 82.228 82.229" fill="currentColor">
                        <defs>
                            <linearGradient id="flowchart-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#b366cf;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#4353ff;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <g transform="translate(-8.589 -8.727)">
                            <path d="M82.953,27.578a1.175,1.175,0,0,0-.367,1.617c9.023,13.965,7.547,33.555-3.5,46.016l-.113-4.8a1.172,1.172,0,0,0-2.344.055l.18,7.691A1.183,1.183,0,0,0,77.98,79.3c.008,0,7.7-.184,7.719-.18a1.172,1.172,0,0,0-.055-2.344l-4.91.117c11.824-13.207,13.438-34.07,3.836-48.938a1.175,1.175,0,0,0-1.617-.367Z"/>
                            <path d="M70.352,82.723c-13.965,9.023-33.555,7.551-46.016-3.5l4.8-.113a1.172,1.172,0,0,0-.055-2.344l-7.687.18a1.175,1.175,0,0,0-1.141,1.2l.18,7.688a1.172,1.172,0,0,0,2.344-.055l-.117-4.906C35.879,92.692,56.7,94.317,71.6,84.707a1.173,1.173,0,0,0-1.25-1.984Z"/>
                            <path d="M27.809,14.973a1.173,1.173,0,0,0,1.25,1.984c13.965-9.019,33.555-7.547,46.016,3.5l-4.8.113a1.172,1.172,0,0,0,.027,2.344c0,.008,7.711-.187,7.719-.18a1.174,1.174,0,0,0,1.145-1.2l-.18-7.691a1.172,1.172,0,0,0-2.344.055l.117,4.91C63.548,6.985,42.685,5.371,27.817,14.973Z"/>
                            <path d="M11.1,45.711a38.857,38.857,0,0,1,9.219-21.234l.113,4.8a1.172,1.172,0,1,0,2.344-.055l-.18-7.691a1.183,1.183,0,0,0-.359-.812,1.164,1.164,0,0,0-.84-.328l-7.691.18a1.172,1.172,0,0,0,.055,2.344l4.91-.117C6.844,36.008,5.234,56.871,14.836,71.739a1.173,1.173,0,0,0,1.984-1.25A38.966,38.966,0,0,1,11.1,45.716Z"/>
                            <path d="M60.238,40.594a1.818,1.818,0,0,0,1.875-1.273l1.1-3.512a1.8,1.8,0,0,0-.852-2.117L60.32,32.559a13.135,13.135,0,0,0-.184-1.883l1.789-1.5a1.819,1.819,0,0,0,.43-2.242L60.6,23.7a1.819,1.819,0,0,0-2.086-.887l-2.3.625A13.277,13.277,0,0,0,54.7,22.219l.18-2.277a1.82,1.82,0,0,0-1.3-1.871l-3.6-1.062a1.828,1.828,0,0,0-2.1.832l-1.172,2.012a13.085,13.085,0,0,0-1.988.2l-1.547-1.754a1.833,1.833,0,0,0-2.223-.406l-3.328,1.738a1.8,1.8,0,0,0-.906,2.086l.617,2.2a12.867,12.867,0,0,0-1.25,1.488l-2.375-.168a1.823,1.823,0,0,0-1.875,1.27l-1.1,3.512a1.808,1.808,0,0,0,.848,2.117l2.047,1.129a13.717,13.717,0,0,0,.18,1.883l-1.789,1.5a1.808,1.808,0,0,0-.426,2.242l1.758,3.234a1.826,1.826,0,0,0,2.086.891l2.3-.625A13.277,13.277,0,0,0,39.25,43.61l-.18,2.277a1.818,1.818,0,0,0,1.3,1.871l3.6,1.063a1.831,1.831,0,0,0,2.1-.832l1.172-2.012a13.234,13.234,0,0,0,1.992-.2l1.547,1.758a1.84,1.84,0,0,0,2.219.4l3.32-1.734a1.8,1.8,0,0,0,.91-2.086l-.617-2.2a13.475,13.475,0,0,0,1.25-1.488l2.375.172Zm-4.129-1.742a10.775,10.775,0,0,1-1.359,1.609,1.776,1.776,0,0,0-.477,1.773l.586,2.086-2.586,1.352-1.461-1.66a1.829,1.829,0,0,0-1.727-.594,11.52,11.52,0,0,1-2.125.207,1.821,1.821,0,0,0-1.578.906l-1.117,1.918-2.8-.828.172-2.148a1.792,1.792,0,0,0-.816-1.656A10.987,10.987,0,0,1,39.179,40.5a1.846,1.846,0,0,0-1.762-.457l-2.184.594-1.352-2.484,1.7-1.422A1.81,1.81,0,0,0,36.187,35a10.288,10.288,0,0,1-.2-2.062,1.808,1.808,0,0,0-.937-1.582L33.121,30.3l.844-2.7,1.965.141a1.939,1.939,0,0,0,1.934-.766A10.794,10.794,0,0,1,39.23,25.36a1.808,1.808,0,0,0,.469-1.766l-.586-2.086L41.7,20.157l1.469,1.664a1.827,1.827,0,0,0,1.727.586,11.487,11.487,0,0,1,2.125-.207,1.827,1.827,0,0,0,1.578-.906l1.117-1.914,2.8.828-.172,2.148a1.8,1.8,0,0,0,.816,1.656A10.744,10.744,0,0,1,54.8,25.324a1.842,1.842,0,0,0,1.758.457l2.188-.594L60.1,27.664l-1.7,1.43a1.822,1.822,0,0,0-.6,1.727,10.653,10.653,0,0,1,.2,2.055,1.791,1.791,0,0,0,.934,1.59l1.934,1.07-.844,2.7-2.25-.16a1.832,1.832,0,0,0-1.641.785Z"/>
                            <path d="M54.285,29.18c-5.238-9.5-19.242-2.352-14.621,7.461A8.207,8.207,0,0,0,54.285,29.18ZM49.64,38.137c-7.012,3.3-12.121-6.7-5.328-10.445a5.784,5.784,0,0,1,2.656-.641,5.873,5.873,0,0,1,2.672,11.086Z"/>
                        </g>
                    </svg>
                </div>
                <h3>{t("problem.card1_title")}</h3>
                <p>Processi multi-step attraverso diversi tool SaaS, rendono inefficace l’automazione tradizionale e richiedono continui interventi da parte di risorse umane.</p>
            </div>

            <div class="problem-card">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 84.064 81.906" fill="currentColor">
                        <g transform="translate(-7.968 -9.047)">
                            <path d="M39.156,27.3c-15.016,0-31.188-2.859-31.188-9.125S24.124,9.047,39.156,9.047s31.188,2.859,31.188,9.125S54.188,27.3,39.156,27.3Zm0-15.125c-18.156,0-28.062,3.969-28.062,6s9.906,6,28.062,6,28.062-3.969,28.062-6-9.906-6-28.062-6Z"/>
                            <path d="M39.156,42.406c-15.016,0-31.188-2.859-31.188-9.125V18.156a1.562,1.562,0,1,1,3.125,0V33.281c0,2.031,9.906,6,28.062,6s28.062-3.969,28.062-6V18.156a1.563,1.563,0,0,1,3.125,0V33.281c0,6.266-16.156,9.125-31.188,9.125Z"/>
                            <path d="M68.766,48.922A1.567,1.567,0,0,1,67.2,47.36v-14a1.562,1.562,0,0,1,3.125,0v14A1.567,1.567,0,0,1,68.766,48.922Z"/>
                            <path d="M39.156,57.594c-15.016,0-31.188-2.859-31.188-9.125V33.36a1.562,1.562,0,1,1,3.125,0V48.469c0,2.031,9.906,6,28.062,6a104,104,0,0,0,13.75-.875,1.56,1.56,0,1,1,.406,3.094,107.284,107.284,0,0,1-14.156.906Z"/>
                            <path d="M39.156,72.812c-15.016,0-31.188-2.844-31.188-9.109V48.578a1.562,1.562,0,1,1,3.125,0V63.7c0,2.031,9.906,5.984,28.062,5.984,3.141,0,6.266-.125,9.281-.391a1.56,1.56,0,1,1,.266,3.109c-3.094.266-6.312.391-9.531.391Z"/>
                            <path d="M39.156,88.125C24.14,88.125,7.968,85.266,7.968,79V63.875a1.562,1.562,0,1,1,3.125,0V79c0,2.031,9.906,6,28.062,6A96.166,96.166,0,0,0,56.4,83.563a1.566,1.566,0,0,1,.578,3.078,98.244,98.244,0,0,1-17.828,1.5Z"/>
                            <path d="M69.438,90.953A22.578,22.578,0,1,1,92.032,68.375,22.6,22.6,0,0,1,69.438,90.953Zm0-42.047A19.453,19.453,0,1,0,88.907,68.359,19.475,19.475,0,0,0,69.438,48.906Z"/>
                            <path d="M69.438,72.344a1.567,1.567,0,0,1-1.562-1.562V54.063a1.563,1.563,0,0,1,3.125,0V70.782A1.567,1.567,0,0,1,69.438,72.344Z"/>
                            <path d="M69.453,82.016a1.567,1.567,0,0,1-1.562-1.562V77.188a1.563,1.563,0,0,1,3.125,0v3.266A1.567,1.567,0,0,1,69.453,82.016Z"/>
                        </g>
                    </svg>
                </div>
                <h3>{t("problem.card2_title")}</h3>
                <p>{t("problem.card2_desc")}</p>
            </div>

            <div class="problem-card">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 55.903 56" fill="currentColor">
                        <g transform="translate(-4 -4)">
                            <g>
                                <path d="M54.5,20.6l5.2-8.4V12c0-.1.1-.2.1-.3h0V5a1,1,0,0,0-1-1H5A.945.945,0,0,0,4,5v6.6H4a.367.367,0,0,0,.1.3v.2L22.3,41.8v3.5c.033.033,0,1.9,0,1.9V59a1.02,1.02,0,0,0,.5.8.551.551,0,0,0,.5.2.6.6,0,0,0,.4-.1l17.1-8.4a1.05,1.05,0,0,0,.5-.9V43.2a12.97,12.97,0,0,0,5.5,1.2A13.145,13.145,0,0,0,59.9,31.3,12.775,12.775,0,0,0,54.5,20.6ZM57.9,5.9v4.7H5.9V5.9ZM24.1,41,6.7,12.6H57.1l-4.3,7h-.1a6.117,6.117,0,0,0-1.2-.5c-.1,0-.1,0-.2-.1a4.951,4.951,0,0,0-1.3-.4c-.1,0-.2,0-.2-.1a5.853,5.853,0,0,0-1.3-.2h-.2c-.5,0-.9-.1-1.4-.1A13.145,13.145,0,0,0,33.8,31.3a9.7,9.7,0,0,0,.1,1.7,4.331,4.331,0,0,0,.1.5c.1.4.1.7.2,1.1.1.2.1.4.2.6l.3.9.3.6a5.937,5.937,0,0,0,.4.8,2.092,2.092,0,0,0,.4.6c.2.3.3.5.5.8.1.2.3.4.4.6.2.2.4.5.6.7l.5.5a4.349,4.349,0,0,0,.7.6c.2.2.4.3.6.5.2.1.3.3.5.4v3.2H24.3V41.7A1.165,1.165,0,0,0,24.1,41Zm.2,16.5V47.2H39.5V50ZM46.9,42.4a11.2,11.2,0,1,1,4.4-21.5,5.388,5.388,0,0,1,1.3.7,11.212,11.212,0,0,1-5.7,20.8Z"/>
                                <path d="M51.7,26.4a.967.967,0,0,0-1.4,0l-3.5,3.5-3.5-3.5a.99.99,0,0,0-1.4,1.4l3.5,3.5L42,34.7a.967.967,0,0,0,0,1.4.967.967,0,0,0,1.4,0l3.5-3.5,3.5,3.5a.967.967,0,0,0,1.4,0,.967.967,0,0,0,0-1.4l-3.5-3.5,3.5-3.5A.946.946,0,0,0,51.7,26.4Z"/>
                            </g>
                        </g>
                    </svg>
                </div>
                <h3>{t("problem.card3_title")}</h3>
                <p>{t("problem.card3_desc")}</p>
            </div>
        </div>
    </div>
</section>

