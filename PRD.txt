1. <PERSON>tegg<PERSON> con password la pagina Dashboard, nella maniera più semplice possibile. La password può anche essere hardcoded.
2. Installa un plugin astro per le multilingue, e sezione per sezione assicurati di utilizzare delle variabili per ogni testo. Le lingue sono 3 - italiano, inglese e spagnolo. Assicurati che il menu nel navbar sia collegato alle traduzioni. IMPORTANTE Non voglio duplicazioni di file .astro per lingua in una cartella per lingua
3. Nella card della sezione "Le Nostre Soluzioni di Intelligenza Artificiale" togliere lo span "time-saved" e invece aggiungere delle piccole tag indicative dell'uso del case studi.  Ad esempio gli AR avatar possono essere usati como: Virtual HR, Guide Musei Virtuali, ecc. 
4. Aggiusta tutti i link ai bottoni di tutte le pagine, incluso le nav bar
5. il bottone "Richiedi una chiamata conoscitiva gratuita" porta la form di contatto ( in ogni lungua)
6. <PERSON><PERSON><PERSON> che il responsive funzioni in tutte sezioni