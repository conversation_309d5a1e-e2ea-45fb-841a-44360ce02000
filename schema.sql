-- Schema per il database dei contatti
CREATE TABLE IF NOT EXISTS contacts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nome_completo TEXT NOT NULL,
    email TEXT NOT NULL,
    ruolo TEXT,
    nome_azienda TEXT,
    sito_web TEXT,
    telefono TEXT,
    dimensione_azienda TEXT,
    fatturato_annuo TEXT,
    budget_progetto TEXT,
    come_possiamo_aiutare TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_contacts_created_at ON contacts(created_at);
CREATE INDEX IF NOT EXISTS idx_contacts_azienda ON contacts(nome_azienda);

-- <PERSON><PERSON> per tracking delle visite (analytics dinamici)
CREATE TABLE IF NOT EXISTS page_views (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    page_path TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    referrer TEXT,
    country TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_page_views_path ON page_views(page_path);
CREATE INDEX IF NOT EXISTS idx_page_views_created_at ON page_views(created_at);

-- Tabella per contenuti dinamici (blog, news, etc.)
CREATE TABLE IF NOT EXISTS dynamic_content (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image TEXT,
    status TEXT DEFAULT 'draft', -- draft, published, archived
    category TEXT,
    tags TEXT, -- JSON array as text
    meta_title TEXT,
    meta_description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    published_at DATETIME
);

CREATE INDEX IF NOT EXISTS idx_content_slug ON dynamic_content(slug);
CREATE INDEX IF NOT EXISTS idx_content_status ON dynamic_content(status);
CREATE INDEX IF NOT EXISTS idx_content_published_at ON dynamic_content(published_at);

-- Tabella per configurazioni dinamiche del sito
CREATE TABLE IF NOT EXISTS site_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type TEXT DEFAULT 'string', -- string, number, boolean, json
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Inserimento configurazioni di default
INSERT OR IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_title', 'Agentik.ai - IA Autonoma per il Business', 'string', 'Titolo principale del sito'),
('contact_email', '<EMAIL>', 'string', 'Email di contatto principale'),
('analytics_enabled', 'true', 'boolean', 'Abilita tracking analytics'),
('maintenance_mode', 'false', 'boolean', 'Modalità manutenzione'),
('max_contacts_per_day', '100', 'number', 'Limite contatti giornalieri');
