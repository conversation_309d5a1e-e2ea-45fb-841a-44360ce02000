---
import { t } from "i18next";
import { localizePath } from "astro-i18next";
// Hero component
---

<section id="hero" class="hero">
    <!-- Animated Particles -->
    <div class="hero-particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; animation-delay: -2s;"></div>
        <div class="particle" style="left: 30%; animation-delay: -4s;"></div>
        <div class="particle" style="left: 40%; animation-delay: -6s;"></div>
        <div class="particle" style="left: 50%; animation-delay: -8s;"></div>
        <div class="particle" style="left: 60%; animation-delay: -10s;"></div>
        <div class="particle" style="left: 70%; animation-delay: -12s;"></div>
        <div class="particle" style="left: 80%; animation-delay: -14s;"></div>
        <div class="particle" style="left: 90%; animation-delay: -16s;"></div>
        <div class="particle" style="left: 15%; animation-delay: -3s;"></div>
        <div class="particle" style="left: 25%; animation-delay: -7s;"></div>
        <div class="particle" style="left: 35%; animation-delay: -11s;"></div>
        <div class="particle" style="left: 45%; animation-delay: -15s;"></div>
        <div class="particle" style="left: 55%; animation-delay: -1s;"></div>
        <div class="particle" style="left: 65%; animation-delay: -5s;"></div>
        <div class="particle" style="left: 75%; animation-delay: -9s;"></div>
        <div class="particle" style="left: 85%; animation-delay: -13s;"></div>
    </div>

    <!-- Data Stream Effect -->
    <div class="hero-datastream">
        <div class="data-line" style="left: 5%; animation-delay: 0s;"></div>
        <div class="data-line" style="left: 15%; animation-delay: -2s;"></div>
        <div class="data-line" style="left: 25%; animation-delay: -4s;"></div>
        <div class="data-line" style="left: 35%; animation-delay: -1s;"></div>
        <div class="data-line" style="left: 45%; animation-delay: -6s;"></div>
        <div class="data-line" style="left: 55%; animation-delay: -3s;"></div>
        <div class="data-line" style="left: 65%; animation-delay: -5s;"></div>
        <div class="data-line" style="left: 75%; animation-delay: -7s;"></div>
        <div class="data-line" style="left: 85%; animation-delay: -2.5s;"></div>
        <div class="data-line" style="left: 95%; animation-delay: -4.5s;"></div>
    </div>

    <!-- Glowing Orbs -->
    <div class="hero-orbs">
        <div class="glowing-orb" style="top: 15%; left: 10%; animation-delay: 0s;"></div>
        <div class="glowing-orb" style="top: 25%; left: 80%; animation-delay: -3s;"></div>
        <div class="glowing-orb" style="top: 45%; left: 20%; animation-delay: -6s;"></div>
        <div class="glowing-orb" style="top: 65%; left: 70%; animation-delay: -2s;"></div>
        <div class="glowing-orb" style="top: 75%; left: 30%; animation-delay: -8s;"></div>
        <div class="glowing-orb" style="top: 35%; left: 90%; animation-delay: -4s;"></div>
        <div class="glowing-orb" style="top: 55%; left: 5%; animation-delay: -7s;"></div>
        <div class="glowing-orb" style="top: 85%; left: 60%; animation-delay: -1s;"></div>
    </div>

    <div class="hero-content">
        <h1 set:html={t("hero.title")}></h1>
        <p class="subtitle">{t("hero.subtitle")}</p>
        <div class="cta-group">
            <a href={localizePath("/contact")} class="cta-button">
                {t("hero.cta_primary")}
                <span>→</span>
            </a>
            <a href="#soluzioni" class="secondary-button">
                <p>{t("hero.cta_secondary")} <span>↓</span></p>
            </a>
        </div>
    </div>
</section>


