---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';

export const prerender = false;

// Password hardcoded per proteggere la dashboard
const DASHBOARD_PASSWORD = "agentik2025";

// Controlla se l'utente è autenticato
const isAuthenticated = Astro.cookies.get('dashboard_auth')?.value === 'true';

// Se non è autenticato e non sta inviando la password, mostra il form di login
if (!isAuthenticated && Astro.request.method !== 'POST') {
    // Mostra form di login
}

// Se sta inviando la password, verifica
if (Astro.request.method === 'POST') {
    const formData = await Astro.request.formData();
    const password = formData.get('password');

    if (password === DASHBOARD_PASSWORD) {
        // Imposta cookie di autenticazione
        Astro.cookies.set('dashboard_auth', 'true', {
            httpOnly: true,
            secure: true,
            sameSite: 'strict',
            maxAge: 60 * 60 * 24 // 24 ore
        });
        // Redirect per evitare resubmit
        return Astro.redirect('/dashboard');
    }
}

// Questa pagina è dinamica e mostra dati real-time
---

<Layout title="Dashboard - Agentik.ai" description="Dashboard amministrativa per Agentik.ai">
    <Header currentPage="dashboard" />
    <main>
        {!isAuthenticated ? (
            <!-- Form di Login -->
            <section class="login-section" style="min-height: 100vh; display: flex; align-items: center; justify-content: center; background: var(--bg-color);">
                <div class="login-container" style="background: var(--surface-color); padding: 40px; border-radius: 16px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); max-width: 400px; width: 100%;">
                    <h1 style="text-align: center; margin-bottom: 32px; color: var(--text-primary);">Accesso Dashboard</h1>
                    <form method="POST" style="display: flex; flex-direction: column; gap: 20px;">
                        <div>
                            <label for="password" style="display: block; margin-bottom: 8px; color: var(--text-primary); font-weight: 500;">Password:</label>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                required
                                style="width: 100%; padding: 12px 16px; border: 2px solid var(--accent-color); border-radius: 8px; background: var(--bg-color); color: var(--text-primary); font-size: 16px;"
                                placeholder="Inserisci la password"
                            />
                        </div>
                        <button
                            type="submit"
                            class="cta-button"
                            style="width: 100%; justify-content: center;"
                        >
                            Accedi
                        </button>
                        {Astro.request.method === 'POST' && (
                            <p style="color: #ff4444; text-align: center; margin: 0;">Password non corretta</p>
                        )}
                    </form>
                </div>
            </section>
        ) : (
        <section class="dashboard-section">
            <div class="container">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px;">
                    <div>
                        <h1 class="section-title" style="margin-bottom: 8px;">Dashboard Dinamica</h1>
                        <p class="section-subtitle" style="margin: 0;">Statistiche e dati in tempo reale del sito</p>
                    </div>
                    <a href="/api/logout" style="color: var(--text-secondary); text-decoration: none; padding: 8px 16px; border: 1px solid var(--text-secondary); border-radius: 6px; transition: all 0.3s ease;">
                        Logout
                    </a>
                </div>
                
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <h3 id="total-visits">-</h3>
                            <p>Visite Totali (7 giorni)</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <h3 id="unique-visitors">-</h3>
                            <p>Visitatori Unici</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📧</div>
                        <div class="stat-content">
                            <h3 id="total-contacts">-</h3>
                            <p>Contatti Ricevuti</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🌍</div>
                        <div class="stat-content">
                            <h3 id="top-country">-</h3>
                            <p>Paese Principale</p>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Section -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <h3>Pagine Più Visitate</h3>
                        <div id="pages-chart" class="chart-content">
                            <div class="loading">Caricamento dati...</div>
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <h3>Contatti per Dimensione Azienda</h3>
                        <div id="company-size-chart" class="chart-content">
                            <div class="loading">Caricamento dati...</div>
                        </div>
                    </div>
                </div>
                
                <!-- Ultimi Contatti Reali -->
                <div class="realtime-section">
                    <h3>Ultimi Contatti Ricevuti</h3>
                    <div id="contacts-list" class="contacts-list">
                        <div class="loading">Caricamento contatti...</div>
                    </div>
                </div>

                <!-- Ultime Visite Reali -->
                <div class="realtime-section">
                    <h3>Ultime Visite</h3>
                    <div id="visits-list" class="visits-list">
                        <div class="loading">Caricamento visite...</div>
                    </div>
                </div>
            </div>
        </section>
        )}
    </main>
    <Footer />
    
    <script>
        // Carica DATI REALI dal database
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/dashboard?type=all');
                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.error || 'Errore nel caricamento');
                }

                const data = result.data;

                // Aggiorna statistiche REALI
                if (data.visits?.stats) {
                    document.getElementById('total-visits')!.textContent = data.visits.stats.total_visits?.toString() || '0';
                    document.getElementById('unique-visitors')!.textContent = data.visits.stats.unique_visitors?.toString() || '0';
                }

                if (data.contacts?.stats) {
                    document.getElementById('total-contacts')!.textContent = data.contacts.stats.total_contacts?.toString() || '0';
                }

                if (data.visits?.by_country && data.visits.by_country.length > 0) {
                    document.getElementById('top-country')!.textContent = data.visits.by_country[0].country || 'N/A';
                }

                // Aggiorna grafici con dati REALI
                updatePagesChart(data.visits?.by_page || []);
                updateCompanySizeChart(data.contacts?.by_company_size || []);

                // Mostra contatti REALI
                updateContactsList(data.contacts?.recent || []);

                // Mostra visite REALI
                updateVisitsList(data.visits?.recent || []);

            } catch (error) {
                console.error('Errore nel caricare i dati REALI:', error);
                document.querySelectorAll('.loading').forEach(el => {
                    el.textContent = 'Errore nel caricamento dati reali';
                });
            }
        }
        
        function updatePagesChart(pages: any[]) {
            const chartEl = document.getElementById('pages-chart');
            if (!chartEl) return;

            if (pages.length === 0) {
                chartEl.innerHTML = '<div class="no-data">Nessuna visita registrata</div>';
                return;
            }

            const maxVisits = Math.max(...pages.map(p => p.visits));
            const html = pages.slice(0, 5).map(page => `
                <div class="chart-bar">
                    <span class="bar-label">${page.page_path}</span>
                    <div class="bar-container">
                        <div class="bar-fill" style="width: ${(page.visits / maxVisits) * 100}%"></div>
                        <span class="bar-value">${page.visits} visite</span>
                    </div>
                </div>
            `).join('');

            chartEl.innerHTML = html;
        }

        function updateCompanySizeChart(contacts: any[]) {
            const chartEl = document.getElementById('company-size-chart');
            if (!chartEl) return;

            if (contacts.length === 0) {
                chartEl.innerHTML = '<div class="no-data">Nessun contatto registrato</div>';
                return;
            }

            const maxCount = Math.max(...contacts.map(c => c.count));
            const html = contacts.map(contact => `
                <div class="chart-bar">
                    <span class="bar-label">${contact.dimensione_azienda || 'Non specificato'}</span>
                    <div class="bar-container">
                        <div class="bar-fill" style="width: ${(contact.count / maxCount) * 100}%"></div>
                        <span class="bar-value">${contact.count} contatti</span>
                    </div>
                </div>
            `).join('');

            chartEl.innerHTML = html;
        }

        // Mostra contatti REALI
        function updateContactsList(contacts: any[]) {
            const listEl = document.getElementById('contacts-list');
            if (!listEl) return;

            if (contacts.length === 0) {
                listEl.innerHTML = '<div class="no-data">Nessun contatto ricevuto ancora</div>';
                return;
            }

            const html = `
                <div class="data-table-header">
                    <span>Nome</span>
                    <span>Email</span>
                    <span>Azienda</span>
                    <span>Budget</span>
                    <span>Data</span>
                </div>
                ${contacts.map(contact => `
                    <div class="data-table-row">
                        <span class="contact-name">${contact.nome_completo}</span>
                        <span class="contact-email">${contact.email}</span>
                        <span class="contact-company">${contact.nome_azienda || 'N/A'}</span>
                        <span class="contact-budget">${contact.budget_progetto || 'N/A'}</span>
                        <span class="contact-date">${new Date(contact.created_at).toLocaleDateString('it-IT')}</span>
                    </div>
                `).join('')}
            `;

            listEl.innerHTML = html;
        }

        // Mostra visite REALI
        function updateVisitsList(visits: any[]) {
            const listEl = document.getElementById('visits-list');
            if (!listEl) return;

            if (visits.length === 0) {
                listEl.innerHTML = '<div class="no-data">Nessuna visita registrata ancora</div>';
                return;
            }

            const html = `
                <div class="data-table-header">
                    <span>Pagina</span>
                    <span>Paese</span>
                    <span>IP</span>
                    <span>Data/Ora</span>
                </div>
                ${visits.map(visit => `
                    <div class="data-table-row">
                        <span class="visit-page">${visit.page_path}</span>
                        <span class="visit-country">${visit.country || 'N/A'}</span>
                        <span class="visit-ip">${visit.ip_address?.substring(0, 10)}...</span>
                        <span class="visit-date">${new Date(visit.created_at).toLocaleString('it-IT')}</span>
                    </div>
                `).join('')}
            `;

            listEl.innerHTML = html;
        }
        
        // Inizializza dashboard con DATI REALI
        loadDashboardData();

        // Ricarica dati REALI ogni 30 secondi
        setInterval(loadDashboardData, 30000);

        // Mostra ultimo aggiornamento
        function showLastUpdate() {
            const now = new Date().toLocaleString('it-IT');
            console.log(`Dashboard aggiornata con dati reali: ${now}`);
        }

        setInterval(showLastUpdate, 30000);
    </script>
</Layout>
