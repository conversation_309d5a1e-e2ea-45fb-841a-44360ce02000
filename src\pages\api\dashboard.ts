import type { APIRoute } from 'astro';

export const prerender = false;

interface Env {
  DB: D1Database;
  CACHE: KVNamespace;
}

export const GET: APIRoute = async ({ url, locals }) => {
  try {
    const env = locals.runtime?.env as Env;
    const searchParams = url.searchParams;
    const type = searchParams.get('type') || 'all';
    
    if (!env?.DB) {
      return new Response(JSON.stringify({ error: 'Database non disponibile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    let data = {};
    
    if (type === 'all' || type === 'contacts') {
      // Ultimi contatti
      const contactsResult = await env.DB.prepare(`
        SELECT 
          nome_completo,
          email,
          nome_azienda,
          ruolo,
          dimensione_azienda,
          budget_progetto,
          created_at
        FROM contacts 
        ORDER BY created_at DESC 
        LIMIT 10
      `).all();
      
      // Statistiche contatti
      const contactStatsResult = await env.DB.prepare(`
        SELECT 
          COUNT(*) as total_contacts,
          COUNT(DISTINCT email) as unique_emails,
          COUNT(DISTINCT nome_azienda) as unique_companies
        FROM contacts
      `).first();
      
      // Contatti per dimensione azienda
      const companySizeResult = await env.DB.prepare(`
        SELECT 
          dimensione_azienda,
          COUNT(*) as count
        FROM contacts 
        WHERE dimensione_azienda IS NOT NULL
        GROUP BY dimensione_azienda
        ORDER BY count DESC
      `).all();
      
      // Contatti per budget
      const budgetResult = await env.DB.prepare(`
        SELECT 
          budget_progetto,
          COUNT(*) as count
        FROM contacts 
        WHERE budget_progetto IS NOT NULL
        GROUP BY budget_progetto
        ORDER BY count DESC
      `).all();
      
      data = {
        ...data,
        contacts: {
          recent: contactsResult.results,
          stats: contactStatsResult,
          by_company_size: companySizeResult.results,
          by_budget: budgetResult.results
        }
      };
    }
    
    if (type === 'all' || type === 'visits') {
      // Ultime visite
      const visitsResult = await env.DB.prepare(`
        SELECT 
          page_path,
          country,
          created_at,
          ip_address
        FROM page_views 
        ORDER BY created_at DESC 
        LIMIT 20
      `).all();
      
      // Statistiche visite
      const visitStatsResult = await env.DB.prepare(`
        SELECT 
          COUNT(*) as total_visits,
          COUNT(DISTINCT ip_address) as unique_visitors,
          COUNT(DISTINCT page_path) as unique_pages
        FROM page_views
      `).first();
      
      // Visite per pagina
      const pageStatsResult = await env.DB.prepare(`
        SELECT 
          page_path,
          COUNT(*) as visits,
          COUNT(DISTINCT ip_address) as unique_visitors
        FROM page_views 
        GROUP BY page_path
        ORDER BY visits DESC
        LIMIT 10
      `).all();
      
      // Visite per paese
      const countryStatsResult = await env.DB.prepare(`
        SELECT 
          country,
          COUNT(*) as visits
        FROM page_views 
        WHERE country != 'unknown' AND country IS NOT NULL
        GROUP BY country
        ORDER BY visits DESC
        LIMIT 10
      `).all();
      
      // Visite oggi
      const todayVisitsResult = await env.DB.prepare(`
        SELECT COUNT(*) as today_visits
        FROM page_views 
        WHERE date(created_at) = date('now')
      `).first();
      
      data = {
        ...data,
        visits: {
          recent: visitsResult.results,
          stats: visitStatsResult,
          by_page: pageStatsResult.results,
          by_country: countryStatsResult.results,
          today: todayVisitsResult
        }
      };
    }
    
    if (type === 'all' || type === 'settings') {
      // Configurazioni sito
      const settingsResult = await env.DB.prepare(`
        SELECT setting_key, setting_value, setting_type, updated_at
        FROM site_settings
        ORDER BY setting_key
      `).all();
      
      data = {
        ...data,
        settings: settingsResult.results
      };
    }
    
    return new Response(JSON.stringify({
      success: true,
      data,
      generated_at: new Date().toISOString()
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=60' // Cache 1 minuto
      }
    });
    
  } catch (error) {
    console.error('Errore nel recuperare dati dashboard:', error);
    return new Response(JSON.stringify({ 
      success: false,
      error: 'Errore interno del server' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
