import type { APIRoute } from 'astro';

export const prerender = false;

interface Env {
  DB: D1Database;
  CACHE: KVNamespace;
}

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const env = locals.runtime?.env as Env;
    const data = await request.json();
    
    const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';
    const userAgent = request.headers.get('User-Agent') || 'unknown';
    const country = request.headers.get('CF-IPCountry') || 'unknown';
    
    // Salva la visita nel database
    if (env?.DB) {
      await env.DB.prepare(`
        INSERT INTO page_views (page_path, ip_address, user_agent, referrer, country)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        data.page || '/',
        clientIP,
        userAgent,
        data.referrer || '',
        country
      ).run();
    }
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Errore analytics:', error);
    return new Response(JSON.stringify({ success: false }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// API per ottenere statistiche
export const GET: APIRoute = async ({ url, locals }) => {
  try {
    const env = locals.runtime?.env as Env;
    const searchParams = url.searchParams;
    const days = parseInt(searchParams.get('days') || '7');
    
    if (!env?.DB) {
      return new Response(JSON.stringify({ error: 'Database non disponibile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Statistiche visite
    const visitsResult = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_visits,
        COUNT(DISTINCT ip_address) as unique_visitors,
        page_path,
        COUNT(*) as page_visits
      FROM page_views 
      WHERE created_at >= datetime('now', '-${days} days')
      GROUP BY page_path
      ORDER BY page_visits DESC
    `).all();
    
    // Statistiche contatti
    const contactsResult = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_contacts,
        COUNT(DISTINCT email) as unique_contacts,
        dimensione_azienda,
        COUNT(*) as contacts_by_size
      FROM contacts 
      WHERE created_at >= datetime('now', '-${days} days')
      GROUP BY dimensione_azienda
    `).all();
    
    // Paesi più attivi
    const countriesResult = await env.DB.prepare(`
      SELECT country, COUNT(*) as visits
      FROM page_views 
      WHERE created_at >= datetime('now', '-${days} days')
        AND country != 'unknown'
      GROUP BY country
      ORDER BY visits DESC
      LIMIT 10
    `).all();
    
    const stats = {
      period_days: days,
      visits: visitsResult.results,
      contacts: contactsResult.results,
      countries: countriesResult.results,
      generated_at: new Date().toISOString()
    };
    
    return new Response(JSON.stringify(stats), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300' // Cache 5 minuti
      }
    });
    
  } catch (error) {
    console.error('Errore nel recuperare statistiche:', error);
    return new Response(JSON.stringify({ error: 'Errore interno' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
