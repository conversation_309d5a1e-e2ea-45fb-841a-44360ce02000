---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Hero from '../components/Hero.astro';
import Partners from '../components/Partners.astro';
import ProblemSection from '../components/ProblemSection.astro';
import ProcessSection from '../components/ProcessSection.astro';
import SolutionSection from '../components/SolutionSection.astro';
import CaseStudies from '../components/CaseStudies.astro';
import CTA from '../components/CTA.astro';
import Footer from '../components/Footer.astro';
import i18next from "i18next";

// Initialize i18next for Italian (default)
i18next.changeLanguage("it");
---

<Layout title="Proxy42 Inc | IA Autonoma per il Business">
    <Header currentPage="home" />
    <main>
        <Hero />
        <Partners />
        <ProblemSection />
        <ProcessSection />
        <SolutionSection />
        <CaseStudies />
        <CTA />
    </main>
    <Footer />
</Layout>
