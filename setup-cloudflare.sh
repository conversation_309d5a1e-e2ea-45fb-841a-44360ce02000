#!/bin/bash

echo "🚀 Setup Cloudflare per Agentik.ai"
echo "=================================="

# Verifica che wrangler sia installato
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler non trovato. Installazione..."
    npm install -g wrangler
fi

# Verifica versione Node.js
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 20 ]; then
    echo "❌ Node.js v20+ richiesto. Versione attuale: $(node -v)"
    echo "Aggiorna Node.js e riprova."
    exit 1
fi

echo "✅ Prerequisiti verificati"

# Login a Cloudflare
echo "🔐 Login a Cloudflare..."
wrangler login

# Crea database D1
echo "🗄️ Creazione database D1..."
DB_OUTPUT=$(wrangler d1 create agentik-contacts)
echo "$DB_OUTPUT"

# Estrai database ID
DB_ID=$(echo "$DB_OUTPUT" | grep "database_id" | cut -d'"' -f4)
echo "Database ID: $DB_ID"

# Crea KV namespace
echo "💾 Creazione KV namespace..."
KV_OUTPUT=$(wrangler kv:namespace create "CACHE")
echo "$KV_OUTPUT"

# Estrai KV ID
KV_ID=$(echo "$KV_OUTPUT" | grep "id =" | cut -d'"' -f2)
echo "KV ID: $KV_ID"

# Crea KV namespace per preview
echo "💾 Creazione KV namespace preview..."
KV_PREVIEW_OUTPUT=$(wrangler kv:namespace create "CACHE" --preview)
KV_PREVIEW_ID=$(echo "$KV_PREVIEW_OUTPUT" | grep "id =" | cut -d'"' -f2)
echo "KV Preview ID: $KV_PREVIEW_ID"

# Aggiorna wrangler.toml
echo "📝 Aggiornamento wrangler.toml..."
sed -i "s/INSERISCI_QUI_IL_DATABASE_ID/$DB_ID/g" wrangler.toml
sed -i "s/INSERISCI_QUI_IL_KV_ID/$KV_ID/g" wrangler.toml
sed -i "s/INSERISCI_QUI_IL_KV_PREVIEW_ID/$KV_PREVIEW_ID/g" wrangler.toml

# Applica schema database
echo "🏗️ Applicazione schema database..."
wrangler d1 execute agentik-contacts --file=./schema.sql

# Verifica setup
echo "✅ Verifica setup..."
wrangler d1 execute agentik-contacts --command="SELECT name FROM sqlite_master WHERE type='table';"

echo ""
echo "🎉 Setup completato!"
echo "=================================="
echo "Database ID: $DB_ID"
echo "KV ID: $KV_ID"
echo "KV Preview ID: $KV_PREVIEW_ID"
echo ""
echo "Ora puoi fare il deploy con:"
echo "npm run deploy"
echo ""
echo "Dashboard disponibile su:"
echo "https://tuo-sito.pages.dev/dashboard"
