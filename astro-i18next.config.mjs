/** @type {import('astro-i18next').AstroI18nextConfig} */
export default {
  defaultLocale: "it",
  locales: ["it", "en", "es"],
  namespaces: ["common"],
  defaultNamespace: "common",
  routes: {
    en: {
      "contact": "contact",
      "dashboard": "dashboard"
    },
    es: {
      "contact": "contacto",
      "dashboard": "dashboard"
    }
  },
  showDefaultLocale: false,
  i18nextServer: {
    debug: true,
  },
  i18nextServerPlugins: {
    fsBackend: {
      loadPath: "./public/locales/{{lng}}/{{ns}}.json",
    },
  },
  i18nextClient: {
    debug: true,
  },
  i18nextClientPlugins: {
    httpBackend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },
  },
};
