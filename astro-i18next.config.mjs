/** @type {import('astro-i18next').AstroI18nextConfig} */
export default {
  defaultLocale: "it",
  locales: ["it", "en", "es"],
  namespaces: ["common"],
  defaultNamespace: "common",
  routes: {
    en: {
      "contact": "contact",
      "dashboard": "dashboard"
    },
    es: {
      "contact": "contacto",
      "dashboard": "dashboard"
    }
  },
  showDefaultLocale: false,
  i18nextServer: {
    debug: false,
    fallbackLng: "it",
  },
  i18nextServerPlugins: {
    fsBackend: {
      loadPath: "./public/locales/{{lng}}/{{ns}}.json",
    },
  },
  i18nextClient: {
    debug: false,
    fallbackLng: "it",
  },
  i18nextClientPlugins: {
    httpBackend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },
  },
};
