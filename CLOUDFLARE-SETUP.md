# 🚀 Setup Cloudflare per Agentik.ai

Guida completa per configurare il database e il deploy su Cloudflare Pages.

## 📋 Prerequisiti

1. **Node.js v20+** (verifica con `node -v`)
2. **Account Cloudflare** (gratuito)
3. **Git** installato

## 🔧 Setup Automatico (Consigliato)

### Windows (PowerShell):
```powershell
npm run setup
```

### Linux/Mac (Bash):
```bash
chmod +x setup-cloudflare.sh
./setup-cloudflare.sh
```

## 🔧 Setup Manuale

### 1. Installa Wrangler
```bash
npm install -g wrangler
```

### 2. Login a Cloudflare
```bash
wrangler login
```

### 3. Crea Database D1
```bash
wrangler d1 create agentik-contacts
```

### 4. Crea KV Namespace
```bash
wrangler kv:namespace create "CACHE"
wrangler kv:namespace create "CACHE" --preview
```

### 5. Aggiorna wrangler.toml
Sostituisci i placeholder con gli ID reali ottenuti dai comandi precedenti.

### 6. Applica Schema Database
```bash
wrangler d1 execute agentik-contacts --file=./schema.sql
```

## 🚀 Deploy

### Deploy Automatico (Git + Cloudflare Pages)
1. Push su GitHub
2. Connetti repository a Cloudflare Pages
3. Build settings:
   - Build command: `npm run build`
   - Build output: `dist`

### Deploy Manuale
```bash
npm run deploy
```

## 📊 Funzionalità Dinamiche

### Database Tables
- **contacts** - Form contatti
- **page_views** - Analytics visite
- **dynamic_content** - Contenuti CMS
- **site_settings** - Configurazioni

### API Endpoints
- `POST /api/contact` - Salva contatti
- `POST /api/analytics` - Traccia visite
- `GET /api/analytics` - Statistiche
- `GET /dashboard` - Dashboard admin

### Dashboard
Accedi alla dashboard su: `https://tuo-sito.pages.dev/dashboard`

## 🔍 Comandi Utili

```bash
# Verifica tabelle database
npm run db:query "SELECT name FROM sqlite_master WHERE type='table';"

# Conta contatti
npm run db:query "SELECT COUNT(*) as total_contacts FROM contacts;"

# Visualizza ultimi contatti
npm run db:query "SELECT nome_completo, email, created_at FROM contacts ORDER BY created_at DESC LIMIT 5;"

# Statistiche visite
npm run db:query "SELECT page_path, COUNT(*) as visits FROM page_views GROUP BY page_path ORDER BY visits DESC;"
```

## 🐛 Troubleshooting

### Errore "Invalid binding"
- Verifica che gli ID nel `wrangler.toml` siano corretti
- Ricontrolla che database e KV siano stati creati

### Errore Node.js version
- Aggiorna Node.js a v20+
- Usa nvm: `nvm install 20 && nvm use 20`

### Database non trovato
```bash
wrangler d1 list
```

### KV non trovato
```bash
wrangler kv:namespace list
```

## 📈 Monitoraggio

### Cloudflare Dashboard
- Analytics: `dash.cloudflare.com` → Pages → Analytics
- Database: `dash.cloudflare.com` → D1
- KV: `dash.cloudflare.com` → KV

### Logs
```bash
wrangler pages deployment tail
```

## 🔒 Sicurezza

- Database D1 è privato per default
- API endpoints validano input
- Rate limiting automatico di Cloudflare
- HTTPS automatico

## 💡 Prossimi Passi

1. **Email Integration**: Aggiungi SendGrid/Resend per notifiche
2. **Authentication**: Sistema login admin
3. **CMS**: Gestione contenuti dinamici
4. **Analytics Avanzati**: Grafici e metriche dettagliate
5. **API Rate Limiting**: Protezione contro spam

---

🎉 **Il tuo sito dinamico è pronto!**
