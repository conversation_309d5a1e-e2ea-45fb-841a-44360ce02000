import type { APIRoute } from 'astro';

export const prerender = false;

interface Env {
  DB: D1Database;
  CACHE: KVNamespace;
}

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const formData = await request.formData();
    const env = locals.runtime?.env as Env;
    
    const contactData = {
      nome_completo: formData.get('nome_completo'),
      email: formData.get('email'),
      ruolo: formData.get('ruolo'),
      nome_azienda: formData.get('nome_azienda'),
      sito_web: formData.get('sito_web'),
      telefono: formData.get('telefono'),
      dimensione_azienda: formData.get('dimensione_azienda'),
      fatturato_annuo: formData.get('fatturato_annuo'),
      budget_progetto: formData.get('budget_progetto'),
      come_possiamo_aiutare: formData.get('come_possiamo_aiutare'),
      timestamp: new Date().toISOString()
    };

    // Validazione base
    if (!contactData.nome_completo || !contactData.email || !contactData.come_possiamo_aiutare) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Nome completo, email e messaggio sono obbligatori'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Validazione email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(contactData.email as string)) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: 'Email non valida' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Ottieni informazioni aggiuntive dalla richiesta
    const clientIP = request.headers.get('CF-Connecting-IP') ||
                     request.headers.get('X-Forwarded-For') ||
                     'unknown';
    const userAgent = request.headers.get('User-Agent') || 'unknown';

    // Salva nel database D1
    if (env?.DB) {
      try {
        const result = await env.DB.prepare(`
          INSERT INTO contacts (
            nome_completo, email, ruolo, nome_azienda, sito_web,
            telefono, dimensione_azienda, fatturato_annuo,
            budget_progetto, come_possiamo_aiutare, ip_address, user_agent
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          contactData.nome_completo,
          contactData.email,
          contactData.ruolo,
          contactData.nome_azienda,
          contactData.sito_web,
          contactData.telefono,
          contactData.dimensione_azienda,
          contactData.fatturato_annuo,
          contactData.budget_progetto,
          contactData.come_possiamo_aiutare,
          clientIP,
          userAgent
        ).run();

        console.log('Contatto salvato nel database:', result.meta);

        // Cache del contatto per analytics
        if (env.CACHE) {
          const cacheKey = `contact:${Date.now()}:${contactData.email}`;
          await env.CACHE.put(cacheKey, JSON.stringify(contactData), {
            expirationTtl: 86400 // 24 ore
          });
        }

      } catch (dbError) {
        console.error('Errore nel salvare nel database:', dbError);
        // Continua comunque, non bloccare per errori DB
      }
    }

    // Qui puoi aggiungere l'invio email tramite:
    // - Cloudflare Email Workers
    // - SendGrid API
    // - Resend API
    // - Webhook a servizi esterni

    console.log('Nuovo contatto ricevuto:', contactData);
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Messaggio inviato con successo! Ti contatteremo presto.' 
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Errore nel processare il form:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: 'Errore interno del server. Riprova più tardi.' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};
