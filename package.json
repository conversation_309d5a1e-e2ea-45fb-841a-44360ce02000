{"name": "agentik-ai-website", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "deploy": "npm run build && npx wrangler pages deploy dist --project-name agentik-ai", "db:create": "wrangler d1 create agentik-contacts", "db:schema": "wrangler d1 execute agentik-contacts --file=./schema.sql", "db:query": "wrangler d1 execute agentik-contacts --command", "kv:create": "wrangler kv:namespace create CACHE", "setup": "powershell -ExecutionPolicy Bypass -File setup-cloudflare.ps1", "astro": "astro"}, "dependencies": {"@astrojs/cloudflare": "^12.6.0", "@astrojs/tailwind": "^5.0.0", "astro": "^5.10.0", "astro-i18next": "^1.0.0-beta.21", "tailwindcss": "^3.0.0"}, "devDependencies": {"@astrojs/check": "^0.3.0", "typescript": "^5.0.0"}}