name = "agentik-ai"
main = "dist/_worker.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]
account_id = "ab70a290d9d159f0efea694d4842da7e"

[assets]
directory = "dist"

[env.production]
name = "agentik-ai"
routes = [
  { pattern = "agentik.ai", zone_name = "agentik.ai" },
  { pattern = "www.agentik.ai", zone_name = "agentik.ai" }
]

[env.preview]
name = "agentik-ai-preview"

# Database D1 per i contatti
[[d1_databases]]
binding = "DB"
database_name = "agentik-contacts"
database_id = "f429deb7-7ed9-4de9-bf9c-a057fdb09a16"

# KV Bindings per le sessioni
[[kv_namespaces]]
binding = "CACHE"
id = "d35dee28554e40adaefa4f05f6c8da8f"
preview_id = "f61ac2fee347443596cf7a03ba9e35dc"

