// Simple i18n system
import itTranslations from '../locales/it/common.json';
import enTranslations from '../locales/en/common.json';
import esTranslations from '../locales/es/common.json';

const translations = {
  it: itTranslations,
  en: enTranslations,
  es: esTranslations
};

export const defaultLocale = 'it';
export const locales = ['it', 'en', 'es'];

export function getTranslation(locale = defaultLocale, key) {
  const keys = key.split('.');
  let translation = translations[locale] || translations[defaultLocale];
  
  for (const k of keys) {
    translation = translation?.[k];
  }
  
  return translation || key;
}

export function t(key, locale = defaultLocale) {
  return getTranslation(locale, key);
}

export function getCurrentLocale(url) {
  const pathname = url.pathname;
  const segments = pathname.split('/').filter(Boolean);
  
  if (segments.length > 0 && locales.includes(segments[0])) {
    return segments[0];
  }
  
  return defaultLocale;
}

export function getLocalizedPath(path, locale) {
  if (locale === defaultLocale) {
    return path;
  }
  
  return `/${locale}${path}`;
}
