import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import cloudflare from '@astrojs/cloudflare';
import astroI18next from 'astro-i18next';

// https://astro.build/config
export default defineConfig({
  integrations: [tailwind(), astroI18next()],
  output: 'server',
  adapter: cloudflare(),
  site: 'https://agentik.ai',
  compressHTML: true,
  build: {
    inlineStylesheets: 'auto'
  },
  i18n: {
    defaultLocale: "it",
    locales: ["it", "en", "es"],
    routing: {
      prefixDefaultLocale: false
    }
  }
});
