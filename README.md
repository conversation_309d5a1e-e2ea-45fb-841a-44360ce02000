# Agentik.ai Website - Astro Version

🚀 **Website aziendale convertito da HTML/CSS/JS a Astro per performance e manutenibilità superiori.**

## 🌟 Deploy
npm run build
wrangler pages deploy dist --project-name agentik-ai

## 🌟 Caratteristiche

- **Framework**: Astro 4.0 con architettura a componenti
- **Styling**: CSS moderno con variabili CSS e design responsive
- **Performance**: Ottimizzato per Core Web Vitals
- **SEO**: Meta tags ottimizzati e struttura semantica
- **Accessibilità**: Design inclusivo e navigazione keyboard-friendly
- **Internazionalizzazione**: Selettore lingua per IT/EN/ES (pronto per i18n)

## 🛠️ Tecnologie

- **Astro** - Framework per siti statici ultra-veloci
- **TypeScript** - Type safety e migliore DX
- **CSS Moderno** - Custom properties, Grid, Flexbox
- **Responsive Design** - Mobile-first approach

## 📁 Struttura del Progetto

```
/
├── public/
│   └── images/           # Immagini statiche
├── src/
│   ├── components/       # Componenti riutilizzabili
│   │   ├── Header.astro
│   │   ├── Hero.astro
│   │   ├── ProblemSection.astro
│   │   ├── ProcessSection.astro
│   │   ├── SolutionSection.astro
│   │   └── Footer.astro
│   ├── layouts/
│   │   └── Layout.astro  # Layout base
│   └── pages/
│       ├── index.astro   # Homepage
│       └── contact.astro # Pagina contatti
├── astro.config.mjs      # Configurazione Astro
└── package.json
```

## 🚀 Comandi

| Comando                   | Azione                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installa le dipendenze                          |
| `npm run dev`             | Avvia il server di sviluppo su `localhost:4321` |
| `npm run build`           | Costruisce il sito per la produzione in `./dist/`|
| `npm run preview`         | Anteprima del build locale                      |
| `npm run astro ...`       | Esegui comandi CLI di Astro                     |

## 🎨 Design System

### Colori
- **Primary Background**: `#0a0a0f`
- **Secondary Background**: `#14141a`
- **Text Primary**: `#ffffff`
- **Text Secondary**: `#a0a0a0`
- **Accent**: `#4a55ff`
- **Gradient**: `#8B5CF6` → `#4A55FF`

### Typography
- **Headings**: Plus Jakarta Sans (Google Fonts)
- **Body**: Work Sans (Google Fonts)
- **Responsive**: Fluid typography con breakpoints ottimizzati

### Breakpoints
- **Desktop**: > 900px
- **Tablet**: 900px - 600px
- **Mobile**: 600px - 480px
- **Small**: < 480px

## 🔧 Componenti Principali

### Header
- Navigation responsive con hamburger menu
- Selettore lingua (IT/EN/ES)
- Smooth scroll e active states

### Hero
- Animazioni di background (gradient, particles, orbs)
- Typography gradiente
- CTA buttons con hover effects

### Problem Section
- Grid responsive di problem cards
- SVG icons con colori coordinati
- Hover animations

### Process Section
- Numeri circolari con gradiente
- Layout a 3 colonne responsive
- Typography gerarchica

### Solution Section
- Cards con immagini e badges
- Grid auto-fit responsive
- Hover effects e transitions

## 📱 Responsive Design

Il sito è completamente responsive con:
- **Mobile-first approach**
- **Flexible grids** che si adattano al contenuto
- **Typography fluida** per leggibilità ottimale
- **Touch-friendly** navigation su mobile
- **Performance ottimizzata** per dispositivi mobili

## 🌍 Internazionalizzazione

Struttura pronta per i18n con:
- Selettore lingua nel header
- Supporto per IT/EN/ES
- Struttura file preparata per traduzioni
- Routing locale-aware (da implementare)

## ⚡ Performance

Ottimizzazioni implementate:
- **Static Site Generation** con Astro
- **Component Islands** per JS minimale
- **Image optimization** ready
- **CSS purging** automatico
- **Bundle splitting** intelligente

## 🚀 Deploy

Il sito può essere deployato su:
- **Vercel** (raccomandato per Astro)
- **Netlify**
- **GitHub Pages**
- **Cloudflare Pages**

```bash
npm run build
# Upload della cartella ./dist/
```

## 📈 Prossimi Passi

1. **Aggiungere immagini** nella cartella `public/images/`
2. **Implementare i18n** completo con Astro i18n
3. **Aggiungere analytics** (Google Analytics, Plausible)
4. **Implementare form handling** per la pagina contatti
5. **Aggiungere animazioni** avanzate con View Transitions API
6. **SEO audit** e ottimizzazioni aggiuntive

## 🤝 Contributi

Per contribuire al progetto:
1. Fork del repository
2. Crea un branch per la feature
3. Commit delle modifiche
4. Push al branch
5. Apri una Pull Request

---

**Agentik.ai** - Trasforma la tua azienda con agenti IA personalizzati 🤖✨
